This project is a minimal **Model Context Protocol (MCP)** server that can search job listings (mocked) and apply to jobs using browser automation (Playwright). It is intentionally conservative: the included application flow demonstrates how to automate form submission on a generic job site and how to track applications in a SQLite DB.


**Important:** Many real job sites (LinkedIn, Indeed, etc.) have anti-bot protection and Terms of Service that restrict automation. Use this code only for learning, for automating your own site, or with explicit permission.


## What you get
- FastAPI MCP server exposing endpoints: `/search_jobs`, `/apply`, `/applications`
- SQLite DB tracking applications
- Playwright-based automation worker (fills a sample form)
- Optional OpenAI integration for tailoring cover letters (placeholder included)
- Mock job data for safe testing


## Files
- `app/main.py` - FastAPI entrypoint (MCP server)
- `app/mcp.py` - high-level MCP functions (search, apply, track)
- `app/playwright_worker.py` - Playwright automation to fill forms
- `app/db.py`, `app/models.py`, `app/schemas.py` - DB layer (SQLAlchemy)
- `templates/cover_template.txt` - fallback cover letter template
- `requirements.txt` - Python dependencies
- `.env.example` - environment variables


## Quickstart
1. Install Python 3.10+ and Node (for Playwright browsers) if not installed.
2. Create a virtualenv: `python -m venv .venv && source .venv/bin/activate` (Linux/macOS) or `python -m venv .venv && .\.venv\\Scripts\\activate` (Windows).
3. Install dependencies:
```bash
pip install -r requirements.txt
playwright install
```
4. Run the server:
```bash
uvicorn app.main:app --reload --port 8000
```
5. Use the API (example):
- `GET /search_jobs?q=engineer&location=remote` - returns mock jobs
- `POST /apply` with JSON body referencing a job id and your profile to simulate applying
- `GET /applications` - list tracked applications


## Notes & Caveats
- This is **not** production-ready. It's a developer skeleton.
- For real job sites, prefer official APIs when available.
- Respect site terms and anti-bot measures. Consider manual review before mass-applying.
