from pydantic import BaseModel
from typing import Optional


class Job(BaseModel):
    id: str
    title: str
    company: str
    location: str
    description: Optional[str]
    apply_url: Optional[str]


class ApplyRequest(BaseModel):
    job_id: str
    applicant_name: str
    applicant_email: str
    resume_text: Optional[str] = None
    cover_letter: Optional[str] = None
    use_openai: Optional[bool] = False


class ApplicationOut(BaseModel):
    id: int
    job_id: str
    job_title: str
    company: str
    location: str
    status: str
    applied_at: str
    notes: Optional[str]