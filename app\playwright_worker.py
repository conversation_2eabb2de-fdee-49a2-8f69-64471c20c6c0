# Playwright-based automation to visit a job's apply_url and fill a form.
# Warning: This script is a generic example and will only reliably work with
# simple, predictable forms. Real job sites vary widely.


import os
from playwright.sync_api import sync_playwright
import time

PLAYWRIGHT_HEADLESS = os.getenv('PLAYWRIGHT_HEADLESS', 'true').lower() == 'true'

def apply_via_playwright(apply_url: str, applicant_name: str, applicant_email: str, resume_text: str, cover_letter: str):
    """Opens the apply_url, fills likely fields, and attempts to submit.
    Returns dict with result info. Use only on sites you are allowed to automate.
    """
    with sync_playwright() as p:
        browser = p.chromium.launch(headless=PLAYWRIGHT_HEADLESS)
        context = browser.new_context()
        page = context.new_page()
        try:
            page.goto(apply_url, timeout=30000)
        except Exception as e:
            browser.close()
        return {"success": False, "error": f"Navigation failed: {e}"}
    
        try:
            for sel in ["input[name=fullname]", "input[name=name]", "input[name=applicant_name]", "input[id*=name]"]:
                if page.query_selector(sel):
                    page.fill(sel, applicant_name)
                    break


            # email
            for sel in ["input[type=email]", "input[name=email]", "input[name=applicant_email]", "input[id*=email]"]:
                if page.query_selector(sel):
                    page.fill(sel, applicant_email)
                    break

            for sel in ["textarea[name=resume]", "textarea[name=cover_letter]", "textarea[name=message]", "textarea"]:
                elem = page.query_selector(sel)
                if elem:
                    # pick first textarea we find; prefer resume-like names
                    content = (resume_text or '') + "\n\n" + (cover_letter or '')
                    elem.fill(content[:5000])
                    break


            # Try to upload file if upload exists (not implemented: requires a file)


            # Try to find and click submit buttons
            for btn in ["button[type=submit]", "button:has-text('Apply')", "button:has-text('Submit')", "input[type=submit]"]:
                el = page.query_selector(btn)
                if el:
                    el.click()
                    time.sleep(2)
                    break


            # Wait a short moment
            time.sleep(2)
            browser.close()
            return {"success": True, "message": "Form interaction attempted — check site for confirmation."}
        except Exception as e:
            browser.close()
            return {"success": False, "error": str(e)}context = browser.new_context()