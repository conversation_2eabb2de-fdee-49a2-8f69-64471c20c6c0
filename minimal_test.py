import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.getcwd())

print("Starting minimal test...")

try:
    print("1. Testing basic imports...")
    from app.schemas import Job
    print("   ✅ Schemas imported")
    
    from app.db import init_db
    print("   ✅ DB module imported")
    
    from app.mcp import search_jobs
    print("   ✅ MCP module imported")
    
    print("2. Testing database initialization...")
    init_db()
    print("   ✅ Database initialized")
    
    print("3. Testing job search...")
    jobs = search_jobs()
    print(f"   ✅ Found {len(jobs)} jobs")
    
    if jobs:
        print(f"   First job: {jobs[0].title} at {jobs[0].company}")
    
    print("4. Testing FastAPI app...")
    from app.main import app
    print("   ✅ FastAPI app imported")
    
    print("\n🎉 All basic tests passed!")
    print("\nTo start the server manually, run:")
    print("uvicorn app.main:app --reload --port 8000")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
