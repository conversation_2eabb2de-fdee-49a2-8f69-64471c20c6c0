# High-level MCP functions (search, apply, track)
]


def search_jobs(q: str = None, location: str = None):
results = []
for j in MOCK_JOBS:
if q and q.lower() not in j['title'].lower() and q.lower() not in j['description'].lower():
continue
if location and location.lower() not in j['location'].lower():
continue
results.append(Job(**j))
return results




def _render_cover(job, applicant_name, applicant_email, resume_text, provided_cover):
# If the user provided a cover letter, use it. Otherwise render fallback template.
if provided_cover:
return provided_cover
try:
tpltext = open(TEMPLATE_PATH).read()
tpl = Template(tpltext)
return tpl.render(hiring_manager='Hiring Team', position=job.title, company=job.company,
skills='software engineering', achievements='product launches',
applicant_name=applicant_name)
except Exception:
return f"Dear Hiring Team,\n\nI am interested in the {job.title} role at {job.company}.\n\nSincerely,\n{applicant_name}"




def apply_to_job(apply_request: ApplyRequest):
# find job
job = None
for j in MOCK_JOBS:
if j['id'] == apply_request.job_id:
job = j
break
if not job:
return {"success": False, "error": "job_not_found"}


cover_letter = _render_cover(job, apply_request.applicant_name, apply_request.applicant_email,
apply_request.resume_text, apply_request.cover_letter)


# Try automated apply via Playwright (best-effort)
apply_url = job.get('apply_url')
if not apply_url:
return {"success": False, "error": "no_apply_url"}


result = apply_via_playwright(apply_url, apply_request.applicant_name, apply_request.applicant_email,
apply_request.resume_text or '', cover_letter)


# Track application in DB
db = SessionLocal()
record = Application(job_id=job['id'], job_title=job['title'], company=job['company'],
location=job['location'], status='applied', notes=str(result))
db.add(record)
db.commit()
db.refresh(record)
db.close()


return {"success": True, "db_id": record.id, "apply_result": result}