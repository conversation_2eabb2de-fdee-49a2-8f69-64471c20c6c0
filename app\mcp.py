# High-level MCP functions (search, apply, track)
import os
from jinja2 import Template
from typing import List
from .schemas import Job, ApplyRequest, ApplicationOut
from .models import Application
from .db import SessionLocal
from .playwright_worker import apply_via_playwright

# Template path for cover letters
TEMPLATE_PATH = os.path.join(os.path.dirname(__file__), '..', 'templates', 'cover_template.txt')

# Mock job data for testing
MOCK_JOBS = [
    {
        "id": "job_001",
        "title": "Senior Software Engineer",
        "company": "TechCorp Inc",
        "location": "Remote",
        "description": "We are looking for a senior software engineer with experience in Python, FastAPI, and modern web technologies.",
        "apply_url": "https://example.com/apply/job_001"
    },
    {
        "id": "job_002",
        "title": "Frontend Developer",
        "company": "WebSolutions LLC",
        "location": "New York, NY",
        "description": "Join our team as a frontend developer working with React, TypeScript, and modern CSS frameworks.",
        "apply_url": "https://example.com/apply/job_002"
    },
    {
        "id": "job_003",
        "title": "Data Scientist",
        "company": "DataTech Analytics",
        "location": "San Francisco, CA",
        "description": "Seeking a data scientist with expertise in machine learning, Python, and statistical analysis.",
        "apply_url": "https://example.com/apply/job_003"
    },
    {
        "id": "job_004",
        "title": "DevOps Engineer",
        "company": "CloudFirst Solutions",
        "location": "Remote",
        "description": "Looking for a DevOps engineer experienced with AWS, Docker, Kubernetes, and CI/CD pipelines.",
        "apply_url": "https://example.com/apply/job_004"
    },
    {
        "id": "job_005",
        "title": "Product Manager",
        "company": "InnovateCorp",
        "location": "Austin, TX",
        "description": "Product manager role focusing on SaaS products, user experience, and cross-functional team leadership.",
        "apply_url": "https://example.com/apply/job_005"
    }
]


def search_jobs(q: str = None, location: str = None) -> List[Job]:
    """Search for jobs based on query and location filters."""
    results = []
    for j in MOCK_JOBS:
        if q and q.lower() not in j['title'].lower() and q.lower() not in j['description'].lower():
            continue
        if location and location.lower() not in j['location'].lower():
            continue
        results.append(Job(**j))
    return results


def _render_cover(job, applicant_name, applicant_email, resume_text, provided_cover):
    """Render cover letter using template or provided text."""
    # If the user provided a cover letter, use it. Otherwise render fallback template.
    if provided_cover:
        return provided_cover
    try:
        tpltext = open(TEMPLATE_PATH).read()
        tpl = Template(tpltext)
        return tpl.render(hiring_manager='Hiring Team', position=job['title'], company=job['company'],
                         skills='software engineering', achievements='product launches',
                         applicant_name=applicant_name)
    except Exception:
        return f"Dear Hiring Team,\n\nI am interested in the {job['title']} role at {job['company']}.\n\nSincerely,\n{applicant_name}"


def apply_to_job(apply_request: ApplyRequest):
    """Apply to a job using the provided application request."""
    # find job
    job = None
    for j in MOCK_JOBS:
        if j['id'] == apply_request.job_id:
            job = j
            break
    if not job:
        return {"success": False, "error": "job_not_found"}

    cover_letter = _render_cover(job, apply_request.applicant_name, apply_request.applicant_email,
                                apply_request.resume_text, apply_request.cover_letter)

    # Try automated apply via Playwright (best-effort)
    apply_url = job.get('apply_url')
    if not apply_url:
        return {"success": False, "error": "no_apply_url"}

    result = apply_via_playwright(apply_url, apply_request.applicant_name, apply_request.applicant_email,
                                 apply_request.resume_text or '', cover_letter)

    # Track application in DB
    db = SessionLocal()
    try:
        record = Application(job_id=job['id'], job_title=job['title'], company=job['company'],
                           location=job['location'], status='applied', notes=str(result))
        db.add(record)
        db.commit()
        db.refresh(record)
        return {"success": True, "db_id": record.id, "apply_result": result}
    finally:
        db.close()


def get_applications() -> List[ApplicationOut]:
    """Get all job applications from the database."""
    db = SessionLocal()
    try:
        applications = db.query(Application).all()
        return [ApplicationOut(
            id=app.id,
            job_id=app.job_id,
            job_title=app.job_title,
            company=app.company,
            location=app.location,
            status=app.status,
            applied_at=app.applied_at.isoformat(),
            notes=app.notes
        ) for app in applications]
    finally:
        db.close()