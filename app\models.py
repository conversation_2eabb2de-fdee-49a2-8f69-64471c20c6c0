from sqlalchemy import Column, Integer, String, DateTime, Text
from .db import Base
import datetime


class Application(Base):
    __tablename__ = 'applications'
    id = Column(Integer, primary_key=True, index=True)
    job_id = Column(String, index=True)
    job_title = Column(String)
    company = Column(String)
    location = Column(String)
    status = Column(String, default='applied')
    applied_at = Column(DateTime, default=datetime.datetime.utcnow)
    notes = Column(Text, nullable=True)