from fastapi import FastAPI, HTTPException, Query
from typing import List, Optional
from contextlib import asynccontextmanager
from .schemas import Job, ApplyRequest, ApplicationOut
from .mcp import search_jobs, apply_to_job, get_applications
from .db import init_db


@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    init_db()
    yield
    # Shutdown (if needed)


# Initialize FastAPI app
app = FastAPI(
    title="Job Application MCP Server",
    description="A Model Context Protocol server for job searching and application automation",
    version="1.0.0",
    lifespan=lifespan
)


@app.get("/")
async def root():
    """Root endpoint with basic information about the API."""
    return {
        "message": "Job Application MCP Server",
        "version": "1.0.0",
        "endpoints": {
            "search_jobs": "/search_jobs",
            "apply": "/apply",
            "applications": "/applications"
        }
    }


@app.get("/search_jobs", response_model=List[Job])
async def search_jobs_endpoint(
    q: Optional[str] = Query(None, description="Search query for job title or description"),
    location: Optional[str] = Query(None, description="Location filter")
):
    """Search for job listings based on query and location filters."""
    try:
        jobs = search_jobs(q=q, location=location)
        return jobs
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error searching jobs: {str(e)}")


@app.post("/apply")
async def apply_to_job_endpoint(apply_request: ApplyRequest):
    """Apply to a job using browser automation."""
    try:
        result = apply_to_job(apply_request)
        if not result.get("success"):
            raise HTTPException(status_code=400, detail=result.get("error", "Application failed"))
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error applying to job: {str(e)}")


@app.get("/applications", response_model=List[ApplicationOut])
async def get_applications_endpoint():
    """Get all job applications from the database."""
    try:
        applications = get_applications()
        return applications
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving applications: {str(e)}")


@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {"status": "healthy", "message": "Job Application MCP Server is running"}


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)