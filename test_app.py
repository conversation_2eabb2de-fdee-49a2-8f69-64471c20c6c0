#!/usr/bin/env python3
"""
Test script for the Job Application MCP Server
"""
import requests
import json
import time
import subprocess
import sys
from threading import Thread


def start_server():
    """Start the FastAPI server in a subprocess"""
    try:
        subprocess.run([
            sys.executable, "-m", "uvicorn", 
            "app.main:app", "--host", "0.0.0.0", "--port", "8000"
        ], check=True)
    except subprocess.CalledProcessError as e:
        print(f"Failed to start server: {e}")


def test_endpoints():
    """Test all the API endpoints"""
    base_url = "http://localhost:8000"
    
    # Wait for server to start
    print("Waiting for server to start...")
    time.sleep(3)
    
    try:
        # Test root endpoint
        print("\n1. Testing root endpoint...")
        response = requests.get(f"{base_url}/")
        print(f"Status: {response.status_code}")
        print(f"Response: {response.json()}")
        
        # Test health endpoint
        print("\n2. Testing health endpoint...")
        response = requests.get(f"{base_url}/health")
        print(f"Status: {response.status_code}")
        print(f"Response: {response.json()}")
        
        # Test search jobs endpoint
        print("\n3. Testing search jobs endpoint...")
        response = requests.get(f"{base_url}/search_jobs")
        print(f"Status: {response.status_code}")
        jobs = response.json()
        print(f"Found {len(jobs)} jobs")
        for job in jobs[:2]:  # Show first 2 jobs
            print(f"  - {job['title']} at {job['company']}")
        
        # Test search with query
        print("\n4. Testing search with query 'engineer'...")
        response = requests.get(f"{base_url}/search_jobs?q=engineer")
        print(f"Status: {response.status_code}")
        jobs = response.json()
        print(f"Found {len(jobs)} jobs matching 'engineer'")
        
        # Test search with location
        print("\n5. Testing search with location 'remote'...")
        response = requests.get(f"{base_url}/search_jobs?location=remote")
        print(f"Status: {response.status_code}")
        jobs = response.json()
        print(f"Found {len(jobs)} remote jobs")
        
        # Test applications endpoint (should be empty initially)
        print("\n6. Testing applications endpoint...")
        response = requests.get(f"{base_url}/applications")
        print(f"Status: {response.status_code}")
        applications = response.json()
        print(f"Found {len(applications)} applications")
        
        # Test apply endpoint (this will fail due to mock URL, but should create DB record)
        print("\n7. Testing apply endpoint...")
        apply_data = {
            "job_id": "job_001",
            "applicant_name": "John Doe",
            "applicant_email": "<EMAIL>",
            "resume_text": "Experienced software engineer with 5 years in Python development.",
            "cover_letter": "I am very interested in this position."
        }
        response = requests.post(f"{base_url}/apply", json=apply_data)
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            print(f"Response: {response.json()}")
        else:
            print(f"Error: {response.text}")
        
        # Check applications again
        print("\n8. Checking applications after apply attempt...")
        response = requests.get(f"{base_url}/applications")
        print(f"Status: {response.status_code}")
        applications = response.json()
        print(f"Found {len(applications)} applications")
        if applications:
            print(f"Latest application: {applications[-1]}")
        
        print("\n✅ All tests completed!")
        
    except requests.exceptions.ConnectionError:
        print("❌ Could not connect to server. Make sure it's running on port 8000.")
    except Exception as e:
        print(f"❌ Test failed with error: {e}")


if __name__ == "__main__":
    print("🚀 Starting Job Application MCP Server tests...")
    
    # Start server in background thread
    server_thread = Thread(target=start_server, daemon=True)
    server_thread.start()
    
    # Run tests
    test_endpoints()
