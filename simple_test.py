#!/usr/bin/env python3
"""
Simple test script to verify the application components work
"""

def test_imports():
    """Test that all imports work correctly"""
    print("Testing imports...")
    try:
        from app.schemas import Job, ApplyRequest, ApplicationOut
        from app.mcp import search_jobs, apply_to_job, get_applications
        from app.db import init_db
        from app.main import app
        print("✅ All imports successful")
        return True
    except Exception as e:
        print(f"❌ Import failed: {e}")
        return False


def test_database():
    """Test database initialization"""
    print("\nTesting database initialization...")
    try:
        from app.db import init_db
        init_db()
        print("✅ Database initialized successfully")
        return True
    except Exception as e:
        print(f"❌ Database initialization failed: {e}")
        return False


def test_search_jobs():
    """Test job search functionality"""
    print("\nTesting job search...")
    try:
        from app.mcp import search_jobs
        
        # Test search all jobs
        all_jobs = search_jobs()
        print(f"✅ Found {len(all_jobs)} total jobs")
        
        # Test search with query
        engineer_jobs = search_jobs(q="engineer")
        print(f"✅ Found {len(engineer_jobs)} jobs matching 'engineer'")
        
        # Test search with location
        remote_jobs = search_jobs(location="remote")
        print(f"✅ Found {len(remote_jobs)} remote jobs")
        
        # Show sample job
        if all_jobs:
            job = all_jobs[0]
            print(f"✅ Sample job: {job.title} at {job.company}")
        
        return True
    except Exception as e:
        print(f"❌ Job search failed: {e}")
        return False


def test_applications():
    """Test applications functionality"""
    print("\nTesting applications...")
    try:
        from app.mcp import get_applications
        applications = get_applications()
        print(f"✅ Found {len(applications)} applications in database")
        return True
    except Exception as e:
        print(f"❌ Applications test failed: {e}")
        return False


def test_cover_letter():
    """Test cover letter rendering"""
    print("\nTesting cover letter rendering...")
    try:
        from app.mcp import _render_cover
        
        # Mock job data
        job = {
            'title': 'Software Engineer',
            'company': 'Test Corp'
        }
        
        # Test with provided cover letter
        provided_cover = "This is my custom cover letter."
        result1 = _render_cover(job, "John Doe", "<EMAIL>", "", provided_cover)
        print(f"✅ Custom cover letter: {result1[:50]}...")
        
        # Test with template fallback
        result2 = _render_cover(job, "John Doe", "<EMAIL>", "", None)
        print(f"✅ Template cover letter: {result2[:50]}...")
        
        return True
    except Exception as e:
        print(f"❌ Cover letter test failed: {e}")
        return False


def main():
    """Run all tests"""
    print("🚀 Running simple tests for Job Application MCP Server...\n")
    
    tests = [
        test_imports,
        test_database,
        test_search_jobs,
        test_applications,
        test_cover_letter
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The application is ready to use.")
        print("\nTo start the server, run:")
        print("  uvicorn app.main:app --reload --port 8000")
        print("\nThen visit http://localhost:8000/docs for the API documentation")
    else:
        print("❌ Some tests failed. Please check the errors above.")


if __name__ == "__main__":
    main()
